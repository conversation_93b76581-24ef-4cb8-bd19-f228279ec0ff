import { PushNotifications, PushNotificationSchema, ActionPerformed, Token } from '@capacitor/push-notifications';
import { Capacitor } from '@capacitor/core';
import { Preferences } from '@capacitor/preferences';
import { fcmE<PERSON>r<PERSON><PERSON><PERSON>, LogLevel, ErrorType } from './fcm-error-handler';
import { deepLinkService } from './deep-link-service';

// Token management constants
const FCM_PREFS_PREFIX = 'fcm_';
const KEY_FCM_TOKEN = `${FCM_PREFS_PREFIX}token`;
const KEY_TOKEN_SENT_TO_SERVER = `${FCM_PREFS_PREFIX}token_sent_to_server`;
const KEY_TOKEN_TIMESTAMP = `${FCM_PREFS_PREFIX}token_timestamp`;

/**
 * Interface for token retrieval callbacks
 */
export interface TokenCallback {
  onTokenReceived(token: string): void;
  onTokenError(error: any): void;
}

/**
 * Interface for token operations callbacks
 */
export interface TokenOperationCallback {
  onSuccess(): void;
  onError(error: any): void;
}

/**
 * Interface for foreground notification display options
 */
export interface ForegroundNotificationOptions {
  showAlert: boolean;
  playSound: boolean;
  setBadge: boolean;
  autoHide: boolean;
  hideDelay?: number; // in milliseconds
}

/**
 * Interface for notification display callback
 */
export interface NotificationDisplayCallback {
  onNotificationDisplayed(notification: PushNotificationSchema): void;
  onNotificationClicked(notification: PushNotificationSchema): void;
  onNotificationDismissed(notification: PushNotificationSchema): void;
}

/**
 * Service for handling push notifications in the application.
 * Provides methods for initializing, registering, and handling push notifications
 * across both iOS and Android platforms.
 * Mirrors the Android FCMTokenManager functionality for consistency.
 */
export class PushNotificationService {
  private static instance: PushNotificationService;
  private initialized = false;
  private token: string | null = null;
  private notificationListeners: ((notification: PushNotificationSchema) => void)[] = [];
  private actionListeners: ((notification: ActionPerformed) => void)[] = [];
  private tokenListeners: ((token: string) => void)[] = [];
  private errorListeners: ((error: any) => void)[] = [];
  private displayCallbacks: NotificationDisplayCallback[] = [];

  // Foreground notification configuration
  private foregroundOptions: ForegroundNotificationOptions = {
    showAlert: true,
    playSound: true,
    setBadge: true,
    autoHide: false,
    hideDelay: 5000
  };

  /**
   * Get the singleton instance of the PushNotificationService
   */
  public static getInstance(): PushNotificationService {
    if (!PushNotificationService.instance) {
      PushNotificationService.instance = new PushNotificationService();
    }
    return PushNotificationService.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {}

  /**
   * Check if push notifications are supported on the current platform
   */
  public isSupported(): boolean {
    return Capacitor.isPluginAvailable('PushNotifications');
  }

  /**
   * Initialize push notifications service
   * This should be called early in the app lifecycle
   * @param baseUrl Optional base URL for deep linking
   */
  public async initialize(baseUrl?: string): Promise<void> {
    if (this.initialized || !this.isSupported()) {
      return;
    }

    fcmErrorHandler.logOperationStart('Push notification initialization');

    try {
      // Initialize deep link service if base URL provided
      if (baseUrl) {
        deepLinkService.setBaseUrl(baseUrl);
      }

      // Configure foreground notification presentation options
      // This is especially important for iOS to show notifications when app is in foreground
      await this.configureForegroundNotifications();

      // Request permission to use push notifications
      // iOS will prompt user and return if they granted permission or not
      // Android will just grant without prompting
      const result = await PushNotifications.requestPermissions();

      if (result.receive === 'granted') {
        // Register with Apple / Google to receive push via APNS/FCM
        await PushNotifications.register();
        fcmErrorHandler.log(LogLevel.INFO, 'Push notification registration initiated');
      } else {
        await fcmErrorHandler.handleFCMError(
          ErrorType.PERMISSION_DENIED,
          'User denied push notification permission',
          { permissionStatus: result.receive }
        );
        this.notifyErrorListeners({
          message: 'Push notification permission denied',
          code: 'permission_denied'
        });
      }

      // Load stored token if available
      await this.loadStoredToken();

      // Setup event listeners
      this.addListeners();
      this.initialized = true;
      fcmErrorHandler.logOperationSuccess('Push notification initialization');
    } catch (error) {
      await fcmErrorHandler.handleFCMError(
        ErrorType.UNKNOWN_ERROR,
        'Failed to initialize push notifications',
        error
      );
      this.notifyErrorListeners(error);
    }
  }

  /**
   * Configure how notifications are displayed when app is in foreground
   * This is especially important for iOS
   */
  private async configureForegroundNotifications(): Promise<void> {
    try {
      // Create default notification channel for Android
      // This mirrors the Android behavior of always showing notifications
      await PushNotifications.createChannel({
        id: 'default',
        name: 'Default Notifications',
        description: 'Default notification channel for the app',
        importance: 5, // High importance - equivalent to Android's IMPORTANCE_HIGH
        visibility: 1, // Public - equivalent to Android's VISIBILITY_PUBLIC
        lights: true,
        vibration: true
      });

      console.log('Notification channel configured');
    } catch (error) {
      // Channel creation might fail on iOS or if already exists
      console.log('Channel creation not supported or already exists:', error);
    }
  }

  /**
   * Load stored token from preferences
   */
  private async loadStoredToken(): Promise<void> {
    try {
      const storedToken = await this.getStoredToken();
      if (storedToken) {
        this.token = storedToken;
        console.log('Loaded stored token:', storedToken);
      }
    } catch (error) {
      console.error('Error loading stored token:', error);
    }
  }

  /**
   * Add event listeners for push notification events
   */
  private addListeners(): void {
    // On success, we should be able to receive notifications
    PushNotifications.addListener('registration', async (token: Token) => {
      fcmErrorHandler.log(LogLevel.INFO, `Push registration success, token: ${token.value}`);

      try {
        // Save token locally
        await this.saveTokenLocally(token.value);
        this.token = token.value;

        // Check if we need to send token to server
        const storedToken = await this.getStoredToken();
        const isTokenSent = await this.isTokenSentToServer();

        if (!isTokenSent || token.value !== storedToken) {
          await this.sendTokenToServer(token.value);
        }

        this.notifyTokenListeners(token.value);
        fcmErrorHandler.logOperationSuccess('Token registration and processing');
      } catch (error) {
        await fcmErrorHandler.handleFCMError(
          ErrorType.TOKEN_RETRIEVAL_FAILED,
          'Failed to process token registration',
          error
        );
        this.notifyErrorListeners(error);
      }
    });

    // Some issue with our setup and push will not work
    PushNotifications.addListener('registrationError', async (error: any) => {
      await fcmErrorHandler.handleFCMError(
        ErrorType.TOKEN_RETRIEVAL_FAILED,
        'Push notification registration failed',
        error
      );
      this.notifyErrorListeners(error);
    });

    // Show us the notification payload if the app is open on our device
    PushNotifications.addListener('pushNotificationReceived', async (notification: PushNotificationSchema) => {
      fcmErrorHandler.log(LogLevel.INFO, `Push notification received: ${notification.title || 'No title'}`);

      try {
        // Process notification payload similar to Android implementation
        await this.processNotificationPayload(notification);
        this.notifyNotificationListeners(notification);
        fcmErrorHandler.log(LogLevel.DEBUG, 'Notification processed successfully');
      } catch (error) {
        await fcmErrorHandler.handleFCMError(
          ErrorType.NOTIFICATION_DISPLAY_FAILED,
          'Failed to process received notification',
          error
        );
        this.notifyErrorListeners(error);
      }
    });

    // Method called when tapping on a notification
    PushNotifications.addListener('pushNotificationActionPerformed', async (notification: ActionPerformed) => {
      fcmErrorHandler.log(LogLevel.INFO, `Push notification action performed: ${notification.actionId}`);

      try {
        // Handle notification action similar to Android implementation
        await this.handleNotificationAction(notification);
        this.notifyActionListeners(notification);
        fcmErrorHandler.log(LogLevel.DEBUG, 'Notification action handled successfully');
      } catch (error) {
        await fcmErrorHandler.handleFCMError(
          ErrorType.NOTIFICATION_DISPLAY_FAILED,
          'Failed to handle notification action',
          error
        );
        this.notifyErrorListeners(error);
      }
    });
  }

  /**
   * Get the current FCM token
   */
  public getToken(): string | null {
    return this.token;
  }

  /**
   * Get the current FCM token with callback support
   * Similar to Android FCMTokenManager.getCurrentToken()
   */
  public async getCurrentToken(callback?: TokenCallback): Promise<string | null> {
    try {
      // If we already have a token, return it
      if (this.token) {
        if (callback) {
          callback.onTokenReceived(this.token);
        }
        return this.token;
      }

      // Try to get stored token
      const storedToken = await this.getStoredToken();
      if (storedToken) {
        this.token = storedToken;
        if (callback) {
          callback.onTokenReceived(storedToken);
        }
        return storedToken;
      }

      // If no token available, the registration listener will handle it
      // when the token becomes available
      if (callback) {
        // Add a one-time listener for when token becomes available
        const tokenListener = (token: string) => {
          callback.onTokenReceived(token);
          this.removeTokenListener(tokenListener);
        };
        this.addTokenListener(tokenListener);
      }

      return null;
    } catch (error) {
      console.error('Error getting current token:', error);
      if (callback) {
        callback.onTokenError(error);
      }
      throw error;
    }
  }

  /**
   * Get the stored token from local storage
   */
  public async getStoredToken(): Promise<string | null> {
    try {
      const result = await Preferences.get({ key: KEY_FCM_TOKEN });
      return result.value;
    } catch (error) {
      console.error('Error getting stored token:', error);
      return null;
    }
  }

  /**
   * Save token to local storage
   */
  public async saveTokenLocally(token: string): Promise<void> {
    fcmErrorHandler.logOperationStart('Save token locally');
    try {
      await Preferences.set({ key: KEY_FCM_TOKEN, value: token });
      await Preferences.set({ key: KEY_TOKEN_TIMESTAMP, value: Date.now().toString() });
      fcmErrorHandler.logOperationSuccess('Save token locally');
    } catch (error) {
      await fcmErrorHandler.handleFCMError(
        ErrorType.TOKEN_RETRIEVAL_FAILED,
        'Failed to save token locally',
        error
      );
      throw error;
    }
  }

  /**
   * Check if token has been sent to server
   */
  public async isTokenSentToServer(): Promise<boolean> {
    try {
      const result = await Preferences.get({ key: KEY_TOKEN_SENT_TO_SERVER });
      return result.value === 'true';
    } catch (error) {
      console.error('Error checking token sent status:', error);
      return false;
    }
  }

  /**
   * Mark token as sent to server
   */
  public async markTokenAsSentToServer(): Promise<void> {
    try {
      await Preferences.set({ key: KEY_TOKEN_SENT_TO_SERVER, value: 'true' });
      console.log('Token marked as sent to server');
    } catch (error) {
      console.error('Error marking token as sent:', error);
      throw error;
    }
  }

  /**
   * Send token to your app server
   * This method should be customized to match your backend API
   */
  public async sendTokenToServer(token: string, callback?: TokenOperationCallback): Promise<void> {
    fcmErrorHandler.logOperationStart('Send token to server');
    fcmErrorHandler.log(LogLevel.DEBUG, `Sending token to server: ${token}`);

    try {
      // Send token to backend server
      // Note: Replace this URL with your actual backend endpoint
      const serverUrl = process.env.VITE_FCM_SERVER_URL || '/api/fcm/token';

      const response = await fetch(serverUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add any required authentication headers here
        },
        body: JSON.stringify({
          token,
          platform: Capacitor.getPlatform(),
          timestamp: Date.now()
        })
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status} ${response.statusText}`);
      }

      await this.markTokenAsSentToServer();
      fcmErrorHandler.logOperationSuccess('Token sent to server successfully');

      if (callback) {
        callback.onSuccess();
      }
    } catch (error) {
      await fcmErrorHandler.handleFCMError(
        ErrorType.NETWORK_ERROR,
        'Failed to send token to server',
        error
      );
      if (callback) {
        callback.onError(error);
      }
      throw error;
    }
  }

  /**
   * Handle token refresh
   * Called when a new token is received
   */
  public async onTokenRefresh(newToken: string): Promise<void> {
    fcmErrorHandler.logOperationStart('Token refresh');
    fcmErrorHandler.log(LogLevel.INFO, `Token refreshed: ${newToken}`);

    try {
      // Save new token
      await this.saveTokenLocally(newToken);
      this.token = newToken;

      // Mark as not sent to server since it's a new token
      await Preferences.set({ key: KEY_TOKEN_SENT_TO_SERVER, value: 'false' });

      // Send new token to server
      await this.sendTokenToServer(newToken, {
        onSuccess: () => {
          fcmErrorHandler.log(LogLevel.INFO, 'Refreshed token successfully sent to server');
        },
        onError: async (error) => {
          await fcmErrorHandler.handleFCMError(
            ErrorType.TOKEN_REFRESH_FAILED,
            'Failed to send refreshed token to server',
            error
          );
        }
      });

      // Notify token listeners
      this.notifyTokenListeners(newToken);
      fcmErrorHandler.logOperationSuccess('Token refresh');
    } catch (error) {
      await fcmErrorHandler.handleFCMError(
        ErrorType.TOKEN_REFRESH_FAILED,
        'Failed to handle token refresh',
        error
      );
      this.notifyErrorListeners(error);
    }
  }

  /**
   * Add a listener for received notifications
   */
  public addNotificationListener(listener: (notification: PushNotificationSchema) => void): void {
    this.notificationListeners.push(listener);
  }

  /**
   * Add a listener for notification actions
   */
  public addActionListener(listener: (notification: ActionPerformed) => void): void {
    this.actionListeners.push(listener);
  }

  /**
   * Add a listener for token updates
   */
  public addTokenListener(listener: (token: string) => void): void {
    this.tokenListeners.push(listener);
    // If we already have a token, notify immediately
    if (this.token) {
      listener(this.token);
    }
  }

  /**
   * Add a listener for errors
   */
  public addErrorListener(listener: (error: any) => void): void {
    this.errorListeners.push(listener);
  }

  /**
   * Remove a notification listener
   */
  public removeNotificationListener(listener: (notification: PushNotificationSchema) => void): void {
    this.notificationListeners = this.notificationListeners.filter(l => l !== listener);
  }

  /**
   * Remove an action listener
   */
  public removeActionListener(listener: (notification: ActionPerformed) => void): void {
    this.actionListeners = this.actionListeners.filter(l => l !== listener);
  }

  /**
   * Remove a token listener
   */
  public removeTokenListener(listener: (token: string) => void): void {
    this.tokenListeners = this.tokenListeners.filter(l => l !== listener);
  }

  /**
   * Remove an error listener
   */
  public removeErrorListener(listener: (error: any) => void): void {
    this.errorListeners = this.errorListeners.filter(l => l !== listener);
  }

  /**
   * Notify all notification listeners
   */
  private notifyNotificationListeners(notification: PushNotificationSchema): void {
    this.notificationListeners.forEach(listener => {
      try {
        listener(notification);
      } catch (error) {
        console.error('Error in notification listener:', error);
      }
    });
  }

  /**
   * Notify all action listeners
   */
  private notifyActionListeners(notification: ActionPerformed): void {
    this.actionListeners.forEach(listener => {
      try {
        listener(notification);
      } catch (error) {
        console.error('Error in action listener:', error);
      }
    });
  }

  /**
   * Notify all token listeners
   */
  private notifyTokenListeners(token: string): void {
    this.tokenListeners.forEach(listener => {
      try {
        listener(token);
      } catch (error) {
        console.error('Error in token listener:', error);
      }
    });
  }

  /**
   * Notify all error listeners
   */
  private notifyErrorListeners(error: any): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(error);
      } catch (err) {
        fcmErrorHandler.log(LogLevel.ERROR, 'Error in error listener', err);
      }
    });
  }

  /**
   * Process notification payload
   * Similar to Android's onMessageReceived method
   */
  private async processNotificationPayload(notification: PushNotificationSchema): Promise<void> {
    fcmErrorHandler.log(LogLevel.DEBUG, 'Processing notification payload', {
      title: notification.title,
      body: notification.body,
      hasData: !!notification.data
    });

    // Extract title and body following Android logic
    let title = notification.title;
    let body = notification.body;

    // Log notification payload if present (Android behavior)
    if (title || body) {
      fcmErrorHandler.log(LogLevel.DEBUG, `Notification payload - Title: ${title}, Body: ${body}`);
    }

    // Check if message contains a data payload (Android behavior)
    if (notification.data && Object.keys(notification.data).length > 0) {
      fcmErrorHandler.log(LogLevel.DEBUG, 'Message data payload:', notification.data);

      // Handle data payload first (Android behavior)
      await this.handleDataPayload(notification.data);

      // Use data payload for title/body if notification payload is empty (Android behavior)
      if (!title || title.trim() === '') {
        title = notification.data.title;
      }
      if (!body || body.trim() === '') {
        body = notification.data.body;
      }
    }

    // Ensure we have something to display (Android fallback behavior)
    if (!title || title.trim() === '') {
      title = 'New Message';
    }
    if (!body || body.trim() === '') {
      body = 'You have a new message';
    }

    // Update notification object with processed values
    notification.title = title;
    notification.body = body;

    fcmErrorHandler.log(LogLevel.DEBUG, `Final notification - Title: ${title}, Body: ${body}`);
  }

  /**
   * Handle data payload
   * Similar to Android's handleDataPayload method
   */
  private async handleDataPayload(data: any): Promise<void> {
    fcmErrorHandler.log(LogLevel.DEBUG, 'Handling data payload');

    try {
      // Extract custom data (Android behavior)
      const title = data.title;
      const body = data.body;
      const action = data.action;
      const url = data.url;
      const type = data.type;
      const priority = data.priority;

      // If we have title and body in data, this is handled by processNotificationPayload
      // This matches Android behavior where data title/body is used for notification display

      // Handle high priority notifications immediately
      if (priority === 'high' || priority === 'critical') {
        fcmErrorHandler.log(LogLevel.INFO, `Processing high priority notification: ${type || action}`);
      }

      // Handle different data types
      if (type) {
        this.handleDataByType(type, data);
      }

      // Handle custom actions (Android behavior)
      if (action) {
        await this.handleCustomAction(action, data);
      }

      // Handle URLs if no specific action
      if (url && !action) {
        await this.handleCustomAction('openUrl', { url, ...data });
      }

      // Store data for background processing if needed
      await this.storeBackgroundData(data);

    } catch (error) {
      fcmErrorHandler.log(LogLevel.ERROR, 'Error handling data payload', error);
    }
  }

  /**
   * Handle data by type
   * Process different notification types for background handling
   */
  private handleDataByType(type: string, data: any): void {
    fcmErrorHandler.log(LogLevel.DEBUG, `Processing notification type: ${type}`);

    switch (type) {
      case 'message':
        this.handleMessageData(data);
        break;
      case 'update':
        this.handleUpdateData(data);
        break;
      case 'sync':
        this.handleSyncData(data);
        break;
      case 'alert':
        this.handleAlertData(data);
        break;
      case 'reminder':
        this.handleReminderData(data);
        break;
      default:
        fcmErrorHandler.log(LogLevel.DEBUG, `Unknown notification type: ${type}`);
        break;
    }
  }

  /**
   * Handle message type notifications
   */
  private handleMessageData(data: any): void {
    fcmErrorHandler.log(LogLevel.DEBUG, 'Processing message notification');
    // Could trigger message sync, update badge count, etc.
    if (data.messageId) {
      fcmErrorHandler.log(LogLevel.INFO, `New message received: ${data.messageId}`);
    }
  }

  /**
   * Handle update type notifications
   */
  private handleUpdateData(data: any): void {
    fcmErrorHandler.log(LogLevel.DEBUG, 'Processing update notification');
    // Could trigger app update check, content refresh, etc.
    if (data.version) {
      fcmErrorHandler.log(LogLevel.INFO, `Update available: ${data.version}`);
    }
  }

  /**
   * Handle sync type notifications
   */
  private handleSyncData(data: any): void {
    fcmErrorHandler.log(LogLevel.DEBUG, 'Processing sync notification');
    // Could trigger data synchronization
    if (data.syncType) {
      fcmErrorHandler.log(LogLevel.INFO, `Sync requested: ${data.syncType}`);
    }
  }

  /**
   * Handle alert type notifications
   */
  private handleAlertData(data: any): void {
    fcmErrorHandler.log(LogLevel.DEBUG, 'Processing alert notification');
    // Could handle critical alerts, warnings, etc.
    if (data.alertLevel) {
      fcmErrorHandler.log(LogLevel.WARNING, `Alert received: ${data.alertLevel}`);
    }
  }

  /**
   * Handle reminder type notifications
   */
  private handleReminderData(data: any): void {
    fcmErrorHandler.log(LogLevel.DEBUG, 'Processing reminder notification');
    // Could handle reminders, scheduled tasks, etc.
    if (data.reminderId) {
      fcmErrorHandler.log(LogLevel.INFO, `Reminder triggered: ${data.reminderId}`);
    }
  }

  /**
   * Store background data for later processing
   */
  private async storeBackgroundData(data: any): Promise<void> {
    try {
      const timestamp = Date.now();
      const backgroundData = {
        data,
        timestamp,
        processed: false
      };

      // Store in preferences for background processing
      const key = `fcm_background_data_${timestamp}`;
      await Preferences.set({
        key,
        value: JSON.stringify(backgroundData)
      });

      fcmErrorHandler.log(LogLevel.DEBUG, `Background data stored: ${key}`);
    } catch (error) {
      fcmErrorHandler.log(LogLevel.ERROR, 'Failed to store background data', error);
    }
  }

  /**
   * Handle custom action from notification
   * Similar to Android's handleCustomAction method
   */
  private async handleCustomAction(action: string, data: any): Promise<void> {
    console.log('Handling custom action:', action, data);

    // Handle different action types (matching Android implementation)
    switch (action) {
      case 'open_url': // Android uses snake_case
      case 'openUrl':  // Also support camelCase for compatibility
        await this.handleOpenUrlAction(data);
        break;
      case 'navigate_to': // Android uses snake_case
      case 'deepLink':    // Also support camelCase for compatibility
        await this.handleDeepLinkAction(data);
        break;
      case 'refresh':
        this.handleRefreshAction(data);
        break;
      case 'sync':
        this.handleSyncAction(data);
        break;
      case 'download':
        this.handleDownloadAction(data);
        break;
      case 'upload':
        this.handleUploadAction(data);
        break;
      case 'notification':
        this.handleLocalNotificationAction(data);
        break;
      case 'badge':
        this.handleBadgeAction(data);
        break;
      default:
        fcmErrorHandler.log(LogLevel.DEBUG, `Unknown action: ${action}`); // Match Android log level
        break;
    }
  }

  /**
   * Handle open URL action (Android: open_url)
   */
  private async handleOpenUrlAction(data: any): Promise<void> {
    const url = data.url;
    if (url) {
      fcmErrorHandler.log(LogLevel.DEBUG, `Action: open URL - ${url}`); // Match Android log format

      try {
        // Check if it's a deep link first
        if (url.includes(window.location.hostname) || url.startsWith('/')) {
          // Handle as deep link
          const success = await deepLinkService.handleDeepLink(url);
          if (success) {
            fcmErrorHandler.log(LogLevel.INFO, 'URL handled as deep link');
            return;
          }
        }

        // Handle as external URL
        // In a real app, you might use the Capacitor App API to open URLs
        // import { App } from '@capacitor/app';
        // await App.openUrl({ url: data.url });

        // For now, open in browser
        if (data.external !== 'false') {
          window.open(url, '_blank');
          fcmErrorHandler.log(LogLevel.INFO, 'External URL opened in browser');
        }
      } catch (error) {
        fcmErrorHandler.log(LogLevel.ERROR, 'Error opening URL', error);
      }
    } else {
      fcmErrorHandler.log(LogLevel.WARNING, 'No URL specified for open_url action');
    }
  }

  /**
   * Handle deep link action (Android: navigate_to)
   */
  private async handleDeepLinkAction(data: any): Promise<void> {
    // Handle both Android 'route' field and iOS 'deepLink' field
    const route = data.route || data.deepLink || data.url;

    if (route) {
      fcmErrorHandler.log(LogLevel.DEBUG, `Action: navigate to - ${route}`); // Match Android log format

      try {
        const success = await deepLinkService.handleNotificationDeepLink(data);
        if (success) {
          fcmErrorHandler.log(LogLevel.INFO, 'Deep link handled successfully');
        } else {
          fcmErrorHandler.log(LogLevel.WARNING, 'Failed to handle deep link');
        }
      } catch (error) {
        fcmErrorHandler.log(LogLevel.ERROR, 'Error handling deep link action', error);
      }
    } else {
      fcmErrorHandler.log(LogLevel.WARNING, 'No route specified for navigate_to action');
    }
  }

  /**
   * Handle refresh action
   */
  private handleRefreshAction(data: any): void {
    fcmErrorHandler.log(LogLevel.INFO, 'Handling refresh action');
    // Could trigger app refresh, data reload, etc.
    if (data.refreshType) {
      fcmErrorHandler.log(LogLevel.DEBUG, `Refresh type: ${data.refreshType}`);
    }
  }

  /**
   * Handle sync action
   */
  private handleSyncAction(data: any): void {
    fcmErrorHandler.log(LogLevel.INFO, 'Handling sync action');
    // Could trigger background sync
    if (data.syncEndpoint) {
      fcmErrorHandler.log(LogLevel.DEBUG, `Sync endpoint: ${data.syncEndpoint}`);
    }
  }

  /**
   * Handle download action
   */
  private handleDownloadAction(data: any): void {
    fcmErrorHandler.log(LogLevel.INFO, 'Handling download action');
    // Could trigger background download
    if (data.downloadUrl) {
      fcmErrorHandler.log(LogLevel.DEBUG, `Download URL: ${data.downloadUrl}`);
    }
  }

  /**
   * Handle upload action
   */
  private handleUploadAction(data: any): void {
    fcmErrorHandler.log(LogLevel.INFO, 'Handling upload action');
    // Could trigger background upload
    if (data.uploadEndpoint) {
      fcmErrorHandler.log(LogLevel.DEBUG, `Upload endpoint: ${data.uploadEndpoint}`);
    }
  }

  /**
   * Handle local notification action
   */
  private handleLocalNotificationAction(data: any): void {
    fcmErrorHandler.log(LogLevel.INFO, 'Handling local notification action');
    // Could trigger local notification display
    if (data.localNotification) {
      fcmErrorHandler.log(LogLevel.DEBUG, 'Triggering local notification');
    }
  }

  /**
   * Handle badge action
   */
  private handleBadgeAction(data: any): void {
    fcmErrorHandler.log(LogLevel.INFO, 'Handling badge action');
    // Could update app badge count
    if (data.badgeCount !== undefined) {
      fcmErrorHandler.log(LogLevel.DEBUG, `Setting badge count: ${data.badgeCount}`);
      // In a real app, you might use the Capacitor App API to set badge
      // import { App } from '@capacitor/app';
      // App.setBadgeCount({ count: data.badgeCount });
    }
  }

  /**
   * Handle notification action
   * Called when user taps on a notification
   */
  private async handleNotificationAction(actionPerformed: ActionPerformed): Promise<void> {
    fcmErrorHandler.log(LogLevel.DEBUG, `Handling notification action: ${actionPerformed.actionId}`);

    const notification = actionPerformed.notification;
    const data = notification.data;

    // Handle action based on notification data
    if (data) {
      const action = data.action;
      if (action) {
        await this.handleCustomAction(action, data);
      }
    }
  }

  /**
   * Get error statistics
   */
  public async getErrorStatistics(): Promise<{
    errorCount: number;
    lastError: string | null;
    lastErrorTime: number;
    isErrorRateTooHigh: boolean;
  }> {
    return {
      errorCount: await fcmErrorHandler.getErrorCount(),
      lastError: await fcmErrorHandler.getLastError(),
      lastErrorTime: await fcmErrorHandler.getLastErrorTime(),
      isErrorRateTooHigh: await fcmErrorHandler.isErrorRateTooHigh()
    };
  }

  /**
   * Create error report for debugging
   */
  public async createErrorReport(): Promise<string> {
    return await fcmErrorHandler.createErrorReport();
  }

  /**
   * Clear error statistics
   */
  public async clearErrorStatistics(): Promise<void> {
    await fcmErrorHandler.clearErrorStats();
  }

  /**
   * Get the error handler instance for advanced usage
   */
  public getErrorHandler() {
    return fcmErrorHandler;
  }

  /**
   * Get the deep link service instance for advanced usage
   */
  public getDeepLinkService() {
    return deepLinkService;
  }

  /**
   * Handle a deep link URL manually
   */
  public async handleDeepLink(url: string): Promise<boolean> {
    return await deepLinkService.handleDeepLink(url);
  }

  /**
   * Create a deep link URL
   */
  public createDeepLink(path: string, params?: any, query?: any): string {
    return deepLinkService.createDeepLink(path, params, query);
  }

  /**
   * Validate notification payload structure
   * Ensures consistency with Android implementation
   */
  public validatePayloadStructure(notification: PushNotificationSchema): {
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check notification structure
    if (!notification.title && !notification.body && !notification.data) {
      issues.push('Notification has no title, body, or data payload');
    }

    // Check data payload structure if present
    if (notification.data) {
      const data = notification.data;

      // Check for Android-compatible action names
      if (data.action) {
        const androidActions = ['open_url', 'navigate_to'];
        const iosActions = ['openUrl', 'deepLink'];

        if (!androidActions.includes(data.action) && !iosActions.includes(data.action)) {
          recommendations.push(`Consider using standard action names: ${androidActions.join(', ')}`);
        }
      }

      // Check for required fields for specific actions
      if (data.action === 'open_url' || data.action === 'openUrl') {
        if (!data.url) {
          issues.push('open_url action requires url field');
        }
      }

      if (data.action === 'navigate_to' || data.action === 'deepLink') {
        if (!data.route && !data.url && !data.deepLink) {
          issues.push('navigate_to action requires route, url, or deepLink field');
        }
      }

      // Check for title/body in data (Android behavior)
      if (data.title || data.body) {
        recommendations.push('Data payload contains title/body - will override notification title/body');
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations
    };
  }

  /**
   * Process pending background data
   * Should be called when app becomes active
   */
  public async processBackgroundData(): Promise<void> {
    fcmErrorHandler.logOperationStart('Process background data');

    try {
      // Get all stored background data
      const keys = await this.getBackgroundDataKeys();

      for (const key of keys) {
        try {
          const result = await Preferences.get({ key });
          if (result.value) {
            const backgroundData = JSON.parse(result.value);

            if (!backgroundData.processed) {
              fcmErrorHandler.log(LogLevel.DEBUG, `Processing background data: ${key}`);

              // Process the data
              await this.processStoredBackgroundData(backgroundData);

              // Mark as processed
              backgroundData.processed = true;
              await Preferences.set({
                key,
                value: JSON.stringify(backgroundData)
              });
            }
          }
        } catch (error) {
          fcmErrorHandler.log(LogLevel.ERROR, `Failed to process background data: ${key}`, error);
        }
      }

      // Clean up old background data (older than 24 hours)
      await this.cleanupOldBackgroundData();

      fcmErrorHandler.logOperationSuccess('Process background data');
    } catch (error) {
      await fcmErrorHandler.handleFCMError(
        ErrorType.UNKNOWN_ERROR,
        'Failed to process background data',
        error
      );
    }
  }

  /**
   * Get all background data keys
   */
  private async getBackgroundDataKeys(): Promise<string[]> {
    try {
      const keys = await Preferences.keys();
      return keys.keys.filter(key => key.startsWith('fcm_background_data_'));
    } catch (error) {
      fcmErrorHandler.log(LogLevel.ERROR, 'Failed to get background data keys', error);
      return [];
    }
  }

  /**
   * Process stored background data
   */
  private async processStoredBackgroundData(backgroundData: any): Promise<void> {
    const { data, timestamp } = backgroundData;

    fcmErrorHandler.log(LogLevel.DEBUG, `Processing stored data from ${new Date(timestamp).toISOString()}`);

    // Re-process the data payload
    await this.handleDataPayload(data);
  }

  /**
   * Clean up old background data
   */
  private async cleanupOldBackgroundData(): Promise<void> {
    try {
      const keys = await this.getBackgroundDataKeys();
      const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000); // 24 hours

      for (const key of keys) {
        try {
          const result = await Preferences.get({ key });
          if (result.value) {
            const backgroundData = JSON.parse(result.value);

            if (backgroundData.timestamp < oneDayAgo) {
              await Preferences.remove({ key });
              fcmErrorHandler.log(LogLevel.DEBUG, `Cleaned up old background data: ${key}`);
            }
          }
        } catch (error) {
          fcmErrorHandler.log(LogLevel.ERROR, `Failed to cleanup background data: ${key}`, error);
        }
      }
    } catch (error) {
      fcmErrorHandler.log(LogLevel.ERROR, 'Failed to cleanup old background data', error);
    }
  }

  /**
   * Handle background notification processing
   * Called when notification is received while app is backgrounded
   */
  public async handleBackgroundNotification(notification: PushNotificationSchema): Promise<void> {
    fcmErrorHandler.log(LogLevel.INFO, 'Handling background notification');

    try {
      // Process notification payload for background handling
      await this.processNotificationPayload(notification);

      // If notification has data payload, handle it for background processing
      if (notification.data) {
        await this.handleBackgroundDataPayload(notification.data);
      }

      fcmErrorHandler.log(LogLevel.DEBUG, 'Background notification processed successfully');
    } catch (error) {
      await fcmErrorHandler.handleFCMError(
        ErrorType.NOTIFICATION_DISPLAY_FAILED,
        'Failed to handle background notification',
        error
      );
    }
  }

  /**
   * Handle background data payload
   * Special handling for data received while app is backgrounded
   */
  private async handleBackgroundDataPayload(data: any): Promise<void> {
    fcmErrorHandler.log(LogLevel.DEBUG, 'Handling background data payload');

    // Store for later processing when app becomes active
    await this.storeBackgroundData(data);

    // Handle immediate background actions
    if (data.immediate === 'true' || data.priority === 'critical') {
      fcmErrorHandler.log(LogLevel.INFO, 'Processing immediate background action');
      await this.handleDataPayload(data);
    }
  }
}

// Export a singleton instance
export const pushNotificationService = PushNotificationService.getInstance();
